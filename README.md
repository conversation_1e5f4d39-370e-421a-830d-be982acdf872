# Spark Kafka Schema Registry Integration

This application demonstrates how to use Apache Spark Structured Streaming to consume data from multiple Kafka topics while dynamically fetching the appropriate schema from Schema Registry for each record.

## Features

- Consumes data from multiple Kafka topics
- Dynamically fetches schemas from Schema Registry based on topic and schema ID
- Handles different schema versions
- Caches schemas for improved performance
- Processes records with appropriate schemas

## Requirements

- Python 3.8+
- Apache Spark 3.4.0+
- Apache Kafka
- Confluent Schema Registry

## Installation

```bash
# Create and activate a virtual environment (optional)
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -e .
```

## Configuration

Edit the `spark_kafka_consumer.py` file to configure the following parameters:

- `bootstrap_servers`: Kafka broker addresses (default: "localhost:9092")
- `schema_registry_url`: Schema Registry URL (default: "http://localhost:8081")
- `topics`: List of Kafka topics to consume (default: ["topic1", "topic2", "topic3"])
- `group_id`: Consumer group ID (default: "spark-kafka-consumer")

## Usage

```bash
spark-submit --packages org.apache.spark:spark-sql-kafka-0-10_2.12:3.4.0,org.apache.spark:spark-avro_2.12:3.4.0 spark_kafka_consumer.py
```

Alternatively, you can run it directly if you have PySpark installed:

```bash
python spark_kafka_consumer.py
```

## How It Works

1. The application creates a Spark Streaming job that connects to Kafka
2. For each record received:
   - It extracts the schema ID from the Avro payload (using Confluent Wire Format)
   - It fetches the corresponding schema from Schema Registry
   - It deserializes the data using the fetched schema
   - It processes the record with the appropriate schema

## Schema Registry Integration

The application uses the Confluent Schema Registry client to fetch schemas. It supports:

- Getting the latest schema for a topic
- Getting a schema by ID
- Getting a schema by version for a specific topic

Schemas are cached to improve performance for repeated schema lookups.

## Handling Multiple Topics with Different Schemas

The application is designed to handle records from multiple topics, each potentially having a different schema. It dynamically determines the appropriate schema for each record based on:

1. The topic the record came from
2. The schema ID embedded in the record (Confluent Wire Format)

This allows for flexible processing of diverse data streams within a single Spark application.
