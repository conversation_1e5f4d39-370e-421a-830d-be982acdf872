# Spark Kafka Consumer with Schema Registry Integration

This project provides Spark streaming applications for consuming Kafka messages with Schema Registry integration, specifically designed for debcdc.* topics.

## Features

- **Schema Registry Integration**: Automatically fetches and caches schemas from Schema Registry
- **Confluent Wire Format Support**: Handles messages serialized with `io.confluent.connect.avro.AvroConverter`
- **Dynamic Topic Discovery**: Automatically discovers debcdc.* topics from Schema Registry subjects
- **Schema Version Handling**: Processes messages with their respective schema versions
- **Key and Value Processing**: Deserializes both message keys and values using their schemas
- **Error Handling**: Comprehensive error handling with separate error streams
- **Multiple Output Formats**: Supports console, parquet, and memory output modes

## Requirements

- Python 3.8+
- Apache Spark 3.4.0+
- Kafka cluster
- Schema Registry (Confluent)

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

Or using uv:
```bash
uv sync
```

## Usage

### Basic Usage

Run the debcdc consumer with default settings:

```bash
python debcdc_spark_consumer.py
```

### Advanced Usage

```bash
python debcdc_spark_consumer.py \
  --bootstrap-servers localhost:9092 \
  --schema-registry-url http://localhost:8081 \
  --output-mode console \
  --checkpoint-location /tmp/debcdc_checkpoint
```

### Command Line Options

- `--bootstrap-servers`: Kafka bootstrap servers (default: localhost:9092)
- `--schema-registry-url`: Schema Registry URL (default: http://localhost:8081)
- `--output-mode`: Output mode - console, parquet, or memory (default: console)
- `--output-path`: Output path for file-based sinks (required for parquet mode)
- `--checkpoint-location`: Spark checkpoint location (default: /tmp/debcdc_checkpoint)

### Output Modes

1. **Console Mode**: Prints processed messages to console
2. **Parquet Mode**: Saves messages to parquet files partitioned by topic
3. **Memory Mode**: Stores messages in memory for testing

## Testing

Run the test script to verify setup:

```bash
python test_debcdc_consumer.py
```

This will:
1. Test Schema Registry connectivity
2. Discover debcdc topics
3. Initialize the consumer
4. Run a short test (optional)

## Architecture

### Components

1. **SchemaRegistryManager**: Manages schema fetching and caching
2. **AvroMessageProcessor**: Handles Avro message deserialization
3. **DebcdcKafkaConsumer**: Main Spark streaming consumer

### Message Processing Flow

1. Subscribe to debcdc.* topic pattern
2. For each message:
   - Extract schema ID from Confluent wire format
   - Fetch schema from Schema Registry (with caching)
   - Deserialize key and value using respective schemas
   - Output processed data with metadata

### Schema Caching

- Schemas are cached by ID to minimize Schema Registry calls
- Subject information is cached for topic discovery
- Cache is maintained in memory during application lifetime

## Configuration

### Kafka Configuration

The consumer uses the following Kafka settings:
- `subscribePattern`: "debcdc\\..*" (regex pattern for debcdc topics)
- `startingOffsets`: "latest"
- `failOnDataLoss`: "false"
- `maxOffsetsPerTrigger`: "1000"

### Spark Configuration

- Packages: spark-sql-kafka, spark-avro
- Adaptive query execution enabled
- Checkpoint location configurable

## Output Schema

Processed messages include:

```json
{
  "topic": "debcdc.table_name",
  "timestamp": 1234567890,
  "offset": 12345,
  "partition": 0,
  "key_schema_id": 101,
  "value_schema_id": 102,
  "key_data": {...},
  "value_data": {...},
  "key_error": null,
  "value_error": null,
  "processing_timestamp": "2024-01-01T12:00:00"
}
```

## Error Handling

The application handles various error scenarios:

- **Schema Not Found**: When schema ID is not in Schema Registry
- **Deserialization Errors**: When Avro deserialization fails
- **Invalid Wire Format**: When message format is incorrect
- **Connection Errors**: When Schema Registry is unavailable

Errors are logged and included in the output stream for monitoring.

## Monitoring

Monitor the application using:

1. **Spark UI**: Available at http://localhost:4040
2. **Logs**: Application logs include detailed processing information
3. **Metrics**: Spark streaming metrics for throughput and latency

## Troubleshooting

### Common Issues

1. **No topics found**: Ensure debcdc.* subjects exist in Schema Registry
2. **Schema Registry connection**: Verify URL and network connectivity
3. **Kafka connection**: Check bootstrap servers and network
4. **Checkpoint issues**: Clear checkpoint directory if needed

### Debug Mode

Enable debug logging:

```python
logging.getLogger().setLevel(logging.DEBUG)
```

## Files

- `debcdc_spark_consumer.py`: Main consumer application
- `test_debcdc_consumer.py`: Test script for validation
- `spark_kafka_consumer.py`: Generic Kafka consumer (legacy)
- `advanced_spark_kafka_consumer.py`: Advanced consumer with more features
