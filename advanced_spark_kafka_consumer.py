#!/usr/bin/env python
"""
Advanced Spark Streaming application that consumes data from multiple Kafka topics
and integrates with Schema Registry to dynamically fetch the appropriate schema
for each record based on its topic. Includes support for schema evolution,
multiple output formats, and comprehensive error handling.
"""

import json
import logging
import os
from dataclasses import dataclass
from typing import Dict, Any, Optional, List, Union

import requests
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroDeserializer
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, from_json, to_json, struct, expr, udf
from pyspark.sql.types import StructType, StringType, BinaryType
import fastavro

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class KafkaConfig:
    """Configuration for Kafka connection."""
    bootstrap_servers: str
    topics: List[str]
    group_id: str
    auto_offset_reset: str = "latest"
    enable_auto_commit: bool = True


@dataclass
class SchemaRegistryConfig:
    """Configuration for Schema Registry connection."""
    url: str
    basic_auth_credentials_source: Optional[str] = None
    basic_auth_user_info: Optional[str] = None


@dataclass
class SparkConfig:
    """Configuration for Spark session."""
    app_name: str
    master: str = "local[*]"
    checkpoint_location: str = "/tmp/checkpoint"
    batch_interval_ms: int = 5000


@dataclass
class OutputConfig:
    """Configuration for output destination."""
    format: str  # "console", "memory", "parquet", "delta", "jdbc", etc.
    options: Dict[str, str] = None
    mode: str = "append"
    partition_by: List[str] = None


class SchemaRegistryManager:
    """
    Manages interactions with the Schema Registry to dynamically fetch schemas
    for different topics with support for schema evolution.
    """
    
    def __init__(self, config: SchemaRegistryConfig):
        """
        Initialize the Schema Registry manager.
        
        Args:
            config: Schema Registry configuration
        """
        self.config = config
        
        # Configure Schema Registry client
        sr_config = {'url': config.url}
        if config.basic_auth_credentials_source:
            sr_config['basic.auth.credentials.source'] = config.basic_auth_credentials_source
        if config.basic_auth_user_info:
            sr_config['basic.auth.user.info'] = config.basic_auth_user_info
            
        self.schema_registry_client = SchemaRegistryClient(sr_config)
        self.schema_cache: Dict[str, Dict[int, str]] = {}  # Cache schemas by topic and version
        self.schema_id_cache: Dict[int, str] = {}  # Cache schemas by ID
        
    def get_latest_schema(self, topic: str) -> Optional[str]:
        """
        Get the latest schema for a given topic.
        
        Args:
            topic: Kafka topic name
        
        Returns:
            The latest schema as a string or None if not found
        """
        try:
            subject_name = f"{topic}-value"
            latest_version = self.schema_registry_client.get_latest_version(subject_name)
            schema_id = latest_version.schema_id
            version = latest_version.version
            
            # Check cache first
            if topic in self.schema_cache and version in self.schema_cache[topic]:
                return self.schema_cache[topic][version]
            
            # Fetch from Schema Registry
            schema = self.schema_registry_client.get_schema(schema_id)
            schema_str = schema.schema_str
            
            # Cache the schema
            if topic not in self.schema_cache:
                self.schema_cache[topic] = {}
            self.schema_cache[topic][version] = schema_str
            self.schema_id_cache[schema_id] = schema_str
            
            return schema_str
        except Exception as e:
            logger.error(f"Error fetching latest schema for topic {topic}: {e}")
            return None
    
    def get_schema_by_id(self, schema_id: int) -> Optional[str]:
        """
        Get a schema by its ID.
        
        Args:
            schema_id: Schema ID
        
        Returns:
            The schema as a string or None if not found
        """
        # Check cache first
        if schema_id in self.schema_id_cache:
            return self.schema_id_cache[schema_id]
        
        try:
            schema = self.schema_registry_client.get_schema(schema_id)
            schema_str = schema.schema_str
            
            # Cache the schema
            self.schema_id_cache[schema_id] = schema_str
            
            return schema_str
        except Exception as e:
            logger.error(f"Error fetching schema with ID {schema_id}: {e}")
            return None
    
    def get_schema_by_version(self, topic: str, version: int) -> Optional[str]:
        """
        Get a schema for a topic by version.
        
        Args:
            topic: Kafka topic name
            version: Schema version
        
        Returns:
            The schema as a string or None if not found
        """
        # Check cache first
        if topic in self.schema_cache and version in self.schema_cache[topic]:
            return self.schema_cache[topic][version]
        
        try:
            subject_name = f"{topic}-value"
            schema_metadata = self.schema_registry_client.get_version(subject_name, version)
            schema_id = schema_metadata.schema_id
            
            # Fetch from Schema Registry
            schema = self.schema_registry_client.get_schema(schema_id)
            schema_str = schema.schema_str
            
            # Cache the schema
            if topic not in self.schema_cache:
                self.schema_cache[topic] = {}
            self.schema_cache[topic][version] = schema_str
            self.schema_id_cache[schema_id] = schema_str
            
            return schema_str
        except Exception as e:
            logger.error(f"Error fetching schema for topic {topic} version {version}: {e}")
            return None
    
    def get_all_versions(self, topic: str) -> List[int]:
        """
        Get all available versions for a topic.
        
        Args:
            topic: Kafka topic name
        
        Returns:
            List of version numbers
        """
        try:
            subject_name = f"{topic}-value"
            versions = self.schema_registry_client.get_versions(subject_name)
            return versions
        except Exception as e:
            logger.error(f"Error fetching versions for topic {topic}: {e}")
            return []
    
    def is_compatible(self, topic: str, new_schema: str) -> bool:
        """
        Check if a new schema is compatible with the latest schema for a topic.
        
        Args:
            topic: Kafka topic name
            new_schema: New schema to check compatibility
        
        Returns:
            True if compatible, False otherwise
        """
        try:
            subject_name = f"{topic}-value"
            compatibility = self.schema_registry_client.test_compatibility(subject_name, new_schema)
            return compatibility
        except Exception as e:
            logger.error(f"Error checking schema compatibility for topic {topic}: {e}")
            return False


class AvroProcessor:
    """
    Processes Avro-encoded messages with schema information.
    """
    
    @staticmethod
    def extract_schema_id(payload: bytes) -> int:
        """
        Extract the schema ID from the Avro payload using Confluent Wire Format.
        
        Args:
            payload: Raw Avro payload with schema ID
        
        Returns:
            Schema ID
        
        Raises:
            ValueError: If payload is too short or invalid
        """
        if not payload or len(payload) < 5:
            raise ValueError("Payload too short to contain schema ID")
        
        # Check magic byte (should be 0)
        if payload[0] != 0:
            raise ValueError(f"Invalid magic byte: {payload[0]}")
        
        # Extract schema ID (next 4 bytes)
        schema_id = int.from_bytes(payload[1:5], byteorder='big')
        return schema_id
    
    @staticmethod
    def deserialize_avro(payload: bytes, schema_str: str) -> Dict[str, Any]:
        """
        Deserialize Avro data using the provided schema.
        
        Args:
            payload: Raw Avro payload (without magic byte and schema ID)
            schema_str: Avro schema as a string
        
        Returns:
            Deserialized data as a dictionary
        """
        try:
            schema = json.loads(schema_str)
            return fastavro.schemaless_reader(payload, schema)
        except Exception as e:
            logger.error(f"Error deserializing Avro data: {e}")
            raise


class KafkaSchemaConsumer:
    """
    Advanced consumer that processes data from multiple Kafka topics with
    dynamic schema handling from Schema Registry.
    """
    
    def __init__(
        self,
        kafka_config: KafkaConfig,
        schema_registry_config: SchemaRegistryConfig,
        spark_config: SparkConfig,
        output_config: OutputConfig
    ):
        """
        Initialize the Kafka Schema Consumer.
        
        Args:
            kafka_config: Kafka connection configuration
            schema_registry_config: Schema Registry configuration
            spark_config: Spark session configuration
            output_config: Output destination configuration
        """
        self.kafka_config = kafka_config
        self.schema_registry_config = schema_registry_config
        self.spark_config = spark_config
        self.output_config = output_config
        
        self.schema_registry_manager = SchemaRegistryManager(schema_registry_config)
        self.avro_processor = AvroProcessor()
        self.spark = self._create_spark_session()
        
    def _create_spark_session(self) -> SparkSession:
        """
        Create a SparkSession with the appropriate configuration.
        
        Returns:
            SparkSession instance
        """
        return (
            SparkSession.builder
            .appName(self.spark_config.app_name)
            .master(self.spark_config.master)
            .config("spark.jars.packages", 
                    "org.apache.spark:spark-sql-kafka-0-10_2.12:3.4.0,"
                    "org.apache.spark:spark-avro_2.12:3.4.0")
            .config("spark.sql.streaming.checkpointLocation", self.spark_config.checkpoint_location)
            .getOrCreate()
        )
    
    def _create_kafka_source(self) -> DataFrame:
        """
        Create a streaming DataFrame from Kafka.
        
        Returns:
            Streaming DataFrame with Kafka data
        """
        return (
            self.spark
            .readStream
            .format("kafka")
            .option("kafka.bootstrap.servers", self.kafka_config.bootstrap_servers)
            .option("subscribe", ",".join(self.kafka_config.topics))
            .option("startingOffsets", self.kafka_config.auto_offset_reset)
            .option("failOnDataLoss", "false")
            .load()
        )
    
    def _process_record(self, topic: str, value: bytes) -> str:
        """
        Process a single record by applying the appropriate schema.
        
        Args:
            topic: Kafka topic name
            value: Raw Avro payload
        
        Returns:
            JSON string with processed data
        """
        try:
            if not value or len(value) < 5:
                return json.dumps({"error": "Payload too short"})
            
            # Extract schema ID from the Avro payload
            schema_id = self.avro_processor.extract_schema_id(value)
            
            # Get schema from Schema Registry
            schema_str = self.schema_registry_manager.get_schema_by_id(schema_id)
            if not schema_str:
                return json.dumps({
                    "error": f"Schema not found for ID {schema_id}",
                    "topic": topic,
                    "schema_id": schema_id
                })
            
            # Deserialize the Avro data
            try:
                data = self.avro_processor.deserialize_avro(value[5:], schema_str)
                
                # Add metadata
                result = {
                    "topic": topic,
                    "schema_id": schema_id,
                    "data": data
                }
                
                return json.dumps(result)
            except Exception as e:
                return json.dumps({
                    "error": f"Deserialization error: {str(e)}",
                    "topic": topic,
                    "schema_id": schema_id
                })
                
        except Exception as e:
            return json.dumps({
                "error": str(e),
                "topic": topic
            })
    
    def _create_output_sink(self, df: DataFrame):
        """
        Create an output sink for the processed data.
        
        Args:
            df: DataFrame with processed data
        
        Returns:
            Streaming query
        """
        writer = df.writeStream.outputMode(self.output_config.mode)
        
        # Set trigger interval
        writer = writer.trigger(processingTime=f"{self.spark_config.batch_interval_ms} milliseconds")
        
        # Configure output format and options
        if self.output_config.options:
            for key, value in self.output_config.options.items():
                writer = writer.option(key, value)
        
        # Set partitioning if specified
        if self.output_config.partition_by:
            writer = writer.partitionBy(*self.output_config.partition_by)
        
        # Return the appropriate sink based on format
        return writer.format(self.output_config.format).start()
    
    def start_streaming(self):
        """
        Start the Spark Streaming job to consume and process data from Kafka.
        """
        # Create a streaming DataFrame from Kafka
        df = self._create_kafka_source()
        
        # Register the UDF for processing records
        process_record_udf = udf(self._process_record, StringType())
        self.spark.udf.register("process_record", process_record_udf)
        
        # Apply the UDF to each record
        processed_df = df.selectExpr(
            "topic", 
            "CAST(key AS STRING) as key",
            "value",
            "timestamp"
        ).withColumn(
            "processed_value", 
            process_record_udf(col("topic"), col("value"))
        )
        
        # Parse the JSON result
        json_schema = StructType().add("topic", StringType()) \
                                 .add("schema_id", StringType()) \
                                 .add("data", StringType()) \
                                 .add("error", StringType())
        
        parsed_df = processed_df.select(
            col("topic"),
            col("key"),
            col("timestamp"),
            from_json(col("processed_value"), json_schema).alias("parsed_value")
        ).select(
            col("topic"),
            col("key"),
            col("timestamp"),
            col("parsed_value.*")
        )
        
        # Handle errors separately
        valid_records = parsed_df.filter(col("error").isNull())
        error_records = parsed_df.filter(col("error").isNotNull())
        
        # Log errors
        error_query = (
            error_records
            .writeStream
            .outputMode("append")
            .format("console")
            .option("truncate", "false")
            .start()
        )
        
        # Process valid records
        valid_query = self._create_output_sink(valid_records)
        
        # Wait for the queries to terminate
        valid_query.awaitTermination()
        error_query.awaitTermination()


def main():
    """
    Main entry point for the application.
    """
    # Kafka configuration
    kafka_config = KafkaConfig(
        bootstrap_servers="localhost:9092",
        topics=["topic1", "topic2", "topic3"],
        group_id="spark-kafka-consumer",
        auto_offset_reset="latest"
    )
    
    # Schema Registry configuration
    schema_registry_config = SchemaRegistryConfig(
        url="http://localhost:8081"
    )
    
    # Spark configuration
    spark_config = SparkConfig(
        app_name="Advanced Kafka Schema Registry Integration",
        master="local[*]",
        checkpoint_location="/tmp/checkpoint",
        batch_interval_ms=5000
    )
    
    # Output configuration
    output_config = OutputConfig(
        format="console",
        options={"truncate": "false"},
        mode="append"
    )
    
    # Create and start the consumer
    consumer = KafkaSchemaConsumer(
        kafka_config=kafka_config,
        schema_registry_config=schema_registry_config,
        spark_config=spark_config,
        output_config=output_config
    )
    
    consumer.start_streaming()


if __name__ == "__main__":
    main()
