#!/usr/bin/env python
"""
Spark Streaming application specifically designed to consume messages from multiple 
Kafka topics with prefix 'debcdc.*' that have schemas in Schema Registry at :8081.
The messages are serialized with io.confluent.connect.avro.AvroConverter.

This script parses all records with their key and values using their respective 
schema and schema version from the Schema Registry.
"""

import json
import logging
import struct
from typing import Dict, Any, Optional, List
from io import BytesIO

import requests
from confluent_kafka.schema_registry import SchemaRegistryClient
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, from_json, udf, expr, when, isnan, isnull
from pyspark.sql.types import StructType, StringType, BinaryType, IntegerType
import avro.schema
import avro.io

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SchemaRegistryManager:
    """
    Manages interactions with the Schema Registry to fetch schemas for debcdc topics.
    """
    
    def __init__(self, schema_registry_url: str = "http://localhost:8081"):
        """
        Initialize the Schema Registry manager.
        
        Args:
            schema_registry_url: URL of the Schema Registry (default: http://localhost:8081)
        """
        self.schema_registry_url = schema_registry_url
        self.schema_registry_client = SchemaRegistryClient({'url': schema_registry_url})
        self.schema_cache: Dict[int, str] = {}  # Cache schemas by ID
        self.subject_cache: Dict[str, List[int]] = {}  # Cache subject versions
        
    def get_schema_by_id(self, schema_id: int) -> Optional[str]:
        """
        Get a schema by its ID from Schema Registry.
        
        Args:
            schema_id: Schema ID
        
        Returns:
            The schema as a string or None if not found
        """
        # Check cache first
        if schema_id in self.schema_cache:
            return self.schema_cache[schema_id]
        
        try:
            schema = self.schema_registry_client.get_schema(schema_id)
            schema_str = schema.schema_str
            
            # Cache the schema
            self.schema_cache[schema_id] = schema_str
            logger.info(f"Cached schema ID {schema_id}")
            
            return schema_str
        except Exception as e:
            logger.error(f"Error fetching schema with ID {schema_id}: {e}")
            return None
    
    def get_all_debcdc_subjects(self) -> List[str]:
        """
        Get all subjects that match the debcdc.* pattern.
        
        Returns:
            List of subject names matching debcdc.*
        """
        try:
            all_subjects = self.schema_registry_client.get_subjects()
            debcdc_subjects = [subject for subject in all_subjects if subject.startswith('debcdc.')]
            logger.info(f"Found {len(debcdc_subjects)} debcdc subjects: {debcdc_subjects}")
            return debcdc_subjects
        except Exception as e:
            logger.error(f"Error fetching subjects: {e}")
            return []
    
    def get_latest_schema_for_subject(self, subject: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest schema for a subject.
        
        Args:
            subject: Subject name
        
        Returns:
            Dictionary with schema_id, version, and schema_str
        """
        try:
            latest_version = self.schema_registry_client.get_latest_version(subject)
            schema = self.schema_registry_client.get_schema(latest_version.schema_id)
            
            return {
                'schema_id': latest_version.schema_id,
                'version': latest_version.version,
                'schema_str': schema.schema_str
            }
        except Exception as e:
            logger.error(f"Error fetching latest schema for subject {subject}: {e}")
            return None


class AvroMessageProcessor:
    """
    Processes Avro messages serialized with io.confluent.connect.avro.AvroConverter.
    """
    
    def __init__(self, schema_registry_manager: SchemaRegistryManager):
        """
        Initialize the Avro message processor.
        
        Args:
            schema_registry_manager: Schema Registry manager instance
        """
        self.schema_registry_manager = schema_registry_manager
    
    @staticmethod
    def extract_schema_id_from_confluent_format(payload: bytes) -> Optional[int]:
        """
        Extract schema ID from Confluent Wire Format.
        Format: [magic_byte(1)][schema_id(4)][avro_payload(n)]
        
        Args:
            payload: Raw message payload
        
        Returns:
            Schema ID or None if invalid format
        """
        try:
            if not payload or len(payload) < 5:
                return None
            
            # Check magic byte (should be 0 for Confluent format)
            magic_byte = payload[0]
            if magic_byte != 0:
                logger.warning(f"Unexpected magic byte: {magic_byte}")
                return None
            
            # Extract schema ID (next 4 bytes, big-endian)
            schema_id = struct.unpack('>I', payload[1:5])[0]
            return schema_id
        except Exception as e:
            logger.error(f"Error extracting schema ID: {e}")
            return None
    
    def deserialize_avro_message(self, payload: bytes, schema_str: str) -> Optional[Dict[str, Any]]:
        """
        Deserialize Avro message using the provided schema.
        
        Args:
            payload: Avro payload (without magic byte and schema ID)
            schema_str: Avro schema as JSON string
        
        Returns:
            Deserialized data as dictionary or None if error
        """
        try:
            # Parse the schema
            schema = avro.schema.parse(schema_str)
            
            # Create a decoder
            bytes_reader = BytesIO(payload)
            decoder = avro.io.BinaryDecoder(bytes_reader)
            reader = avro.io.DatumReader(schema)
            
            # Deserialize the data
            data = reader.read(decoder)
            return data
        except Exception as e:
            logger.error(f"Error deserializing Avro message: {e}")
            return None
    
    def process_message(self, key_payload: bytes, value_payload: bytes) -> Dict[str, Any]:
        """
        Process both key and value payloads of a Kafka message.
        
        Args:
            key_payload: Raw key payload
            value_payload: Raw value payload
        
        Returns:
            Dictionary with processed key and value data
        """
        result = {
            'key_data': None,
            'key_schema_id': None,
            'key_error': None,
            'value_data': None,
            'value_schema_id': None,
            'value_error': None
        }
        
        # Process key if present
        if key_payload:
            try:
                key_schema_id = self.extract_schema_id_from_confluent_format(key_payload)
                if key_schema_id:
                    key_schema = self.schema_registry_manager.get_schema_by_id(key_schema_id)
                    if key_schema:
                        key_data = self.deserialize_avro_message(key_payload[5:], key_schema)
                        result['key_data'] = key_data
                        result['key_schema_id'] = key_schema_id
                    else:
                        result['key_error'] = f"Schema not found for key schema ID: {key_schema_id}"
                else:
                    result['key_error'] = "Could not extract schema ID from key"
            except Exception as e:
                result['key_error'] = f"Error processing key: {str(e)}"
        
        # Process value if present
        if value_payload:
            try:
                value_schema_id = self.extract_schema_id_from_confluent_format(value_payload)
                if value_schema_id:
                    value_schema = self.schema_registry_manager.get_schema_by_id(value_schema_id)
                    if value_schema:
                        value_data = self.deserialize_avro_message(value_payload[5:], value_schema)
                        result['value_data'] = value_data
                        result['value_schema_id'] = value_schema_id
                    else:
                        result['value_error'] = f"Schema not found for value schema ID: {value_schema_id}"
                else:
                    result['value_error'] = "Could not extract schema ID from value"
            except Exception as e:
                result['value_error'] = f"Error processing value: {str(e)}"
        
        return result


class DebcdcKafkaConsumer:
    """
    Spark Streaming consumer for debcdc.* topics with Schema Registry integration.
    """
    
    def __init__(
        self,
        bootstrap_servers: str = "localhost:9092",
        schema_registry_url: str = "http://localhost:8081",
        app_name: str = "DebcdcKafkaConsumer",
        checkpoint_location: str = "/tmp/debcdc_checkpoint"
    ):
        """
        Initialize the debcdc Kafka consumer.
        
        Args:
            bootstrap_servers: Kafka bootstrap servers
            schema_registry_url: Schema Registry URL
            app_name: Spark application name
            checkpoint_location: Checkpoint location for Spark Streaming
        """
        self.bootstrap_servers = bootstrap_servers
        self.schema_registry_url = schema_registry_url
        self.app_name = app_name
        self.checkpoint_location = checkpoint_location
        
        self.schema_registry_manager = SchemaRegistryManager(schema_registry_url)
        self.avro_processor = AvroMessageProcessor(self.schema_registry_manager)
        self.spark = self._create_spark_session()
        
    def _create_spark_session(self) -> SparkSession:
        """
        Create SparkSession with required configurations.
        
        Returns:
            SparkSession instance
        """
        return (
            SparkSession.builder
            .appName(self.app_name)
            .master("local[*]")
            .config("spark.jars.packages", 
                    "org.apache.spark:spark-sql-kafka-0-10_2.12:3.4.0,"
                    "org.apache.spark:spark-avro_2.12:3.4.0")
            .config("spark.sql.streaming.checkpointLocation", self.checkpoint_location)
            .config("spark.sql.adaptive.enabled", "true")
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
            .getOrCreate()
        )
    
    def get_debcdc_topics(self) -> List[str]:
        """
        Get all debcdc topics from Schema Registry subjects.
        
        Returns:
            List of debcdc topic names
        """
        subjects = self.schema_registry_manager.get_all_debcdc_subjects()
        topics = []
        
        for subject in subjects:
            # Extract topic name from subject (remove -key or -value suffix)
            if subject.endswith('-value') or subject.endswith('-key'):
                topic = subject.rsplit('-', 1)[0]
                if topic not in topics:
                    topics.append(topic)
        
        logger.info(f"Discovered debcdc topics: {topics}")
        return topics

    def _process_kafka_record(self, topic: str, key: bytes, value: bytes, timestamp: int, offset: int) -> str:
        """
        Process a single Kafka record with schema resolution.

        Args:
            topic: Kafka topic name
            key: Message key (binary)
            value: Message value (binary)
            timestamp: Message timestamp
            offset: Message offset

        Returns:
            JSON string with processed record data
        """
        try:
            # Process the message using Avro processor
            processed = self.avro_processor.process_message(key, value)

            # Create result with metadata
            result = {
                'topic': topic,
                'timestamp': timestamp,
                'offset': offset,
                'key_schema_id': processed['key_schema_id'],
                'value_schema_id': processed['value_schema_id'],
                'key_data': processed['key_data'],
                'value_data': processed['value_data'],
                'key_error': processed['key_error'],
                'value_error': processed['value_error'],
                'processing_timestamp': None  # Will be set by Spark
            }

            return json.dumps(result, default=str)
        except Exception as e:
            error_result = {
                'topic': topic,
                'timestamp': timestamp,
                'offset': offset,
                'error': f"Processing error: {str(e)}",
                'processing_timestamp': None
            }
            return json.dumps(error_result, default=str)

    def create_streaming_dataframe(self) -> DataFrame:
        """
        Create a streaming DataFrame from Kafka for debcdc topics.

        Returns:
            Streaming DataFrame
        """
        # Get all debcdc topics
        topics = self.get_debcdc_topics()

        if not topics:
            raise ValueError("No debcdc topics found in Schema Registry")

        # Create topic pattern for subscription
        topic_pattern = "debcdc\\..*"

        logger.info(f"Subscribing to topic pattern: {topic_pattern}")
        logger.info(f"Expected topics: {topics}")

        # Create streaming DataFrame
        df = (
            self.spark
            .readStream
            .format("kafka")
            .option("kafka.bootstrap.servers", self.bootstrap_servers)
            .option("subscribePattern", topic_pattern)
            .option("startingOffsets", "latest")
            .option("failOnDataLoss", "false")
            .option("maxOffsetsPerTrigger", "1000")  # Limit records per batch
            .load()
        )

        return df

    def process_stream(self, output_mode: str = "console", output_path: str = None):
        """
        Process the Kafka stream with schema resolution.

        Args:
            output_mode: Output mode ("console", "memory", "parquet", etc.)
            output_path: Output path for file-based sinks
        """
        # Create streaming DataFrame
        kafka_df = self.create_streaming_dataframe()

        # Register UDF for processing records
        process_record_udf = udf(self._process_kafka_record, StringType())

        # Process each record
        processed_df = kafka_df.select(
            col("topic"),
            col("key"),
            col("value"),
            col("timestamp"),
            col("offset"),
            col("partition")
        ).withColumn(
            "processed_data",
            process_record_udf(
                col("topic"),
                col("key"),
                col("value"),
                col("timestamp"),
                col("offset")
            )
        )

        # Parse the JSON result
        json_schema = StructType() \
            .add("topic", StringType()) \
            .add("timestamp", StringType()) \
            .add("offset", StringType()) \
            .add("key_schema_id", IntegerType()) \
            .add("value_schema_id", IntegerType()) \
            .add("key_data", StringType()) \
            .add("value_data", StringType()) \
            .add("key_error", StringType()) \
            .add("value_error", StringType()) \
            .add("error", StringType()) \
            .add("processing_timestamp", StringType())

        final_df = processed_df.select(
            col("topic"),
            col("partition"),
            col("offset"),
            from_json(col("processed_data"), json_schema).alias("parsed")
        ).select(
            col("topic"),
            col("partition"),
            col("offset"),
            col("parsed.*")
        ).withColumn(
            "processing_timestamp",
            expr("current_timestamp()")
        )

        # Separate successful and failed records
        successful_records = final_df.filter(
            col("error").isNull() &
            (col("key_error").isNull() | col("value_error").isNull())
        )

        failed_records = final_df.filter(
            col("error").isNotNull() |
            (col("key_error").isNotNull() & col("value_error").isNotNull())
        )

        # Configure output based on mode
        if output_mode == "console":
            # Output successful records
            success_query = (
                successful_records
                .writeStream
                .outputMode("append")
                .format("console")
                .option("truncate", "false")
                .option("numRows", "20")
                .trigger(processingTime="10 seconds")
                .start()
            )

            # Output failed records to console with different prefix
            error_query = (
                failed_records
                .writeStream
                .outputMode("append")
                .format("console")
                .option("truncate", "false")
                .option("numRows", "10")
                .trigger(processingTime="10 seconds")
                .start()
            )

            # Wait for termination (both queries will run concurrently)
            try:
                success_query.awaitTermination()
            finally:
                if error_query.isActive:
                    error_query.stop()

        elif output_mode == "parquet" and output_path:
            # Output to parquet files
            query = (
                final_df
                .writeStream
                .outputMode("append")
                .format("parquet")
                .option("path", output_path)
                .option("checkpointLocation", f"{self.checkpoint_location}/parquet")
                .partitionBy("topic")
                .trigger(processingTime="30 seconds")
                .start()
            )

            query.awaitTermination()

        elif output_mode == "memory":
            # Output to memory for testing
            query = (
                final_df
                .writeStream
                .outputMode("append")
                .format("memory")
                .queryName("debcdc_stream")
                .trigger(processingTime="5 seconds")
                .start()
            )

            query.awaitTermination()

        else:
            raise ValueError(f"Unsupported output mode: {output_mode}")


def main():
    """
    Main entry point for the debcdc Kafka consumer.
    """
    import argparse

    parser = argparse.ArgumentParser(description="Debcdc Kafka Consumer with Schema Registry")
    parser.add_argument("--bootstrap-servers", default="localhost:9092",
                       help="Kafka bootstrap servers")
    parser.add_argument("--schema-registry-url", default="http://localhost:8081",
                       help="Schema Registry URL")
    parser.add_argument("--output-mode", default="console",
                       choices=["console", "parquet", "memory"],
                       help="Output mode")
    parser.add_argument("--output-path",
                       help="Output path for file-based sinks")
    parser.add_argument("--checkpoint-location", default="/tmp/debcdc_checkpoint",
                       help="Checkpoint location")

    args = parser.parse_args()

    # Create consumer
    consumer = DebcdcKafkaConsumer(
        bootstrap_servers=args.bootstrap_servers,
        schema_registry_url=args.schema_registry_url,
        checkpoint_location=args.checkpoint_location
    )

    # Start processing
    logger.info("Starting debcdc Kafka consumer...")
    logger.info(f"Bootstrap servers: {args.bootstrap_servers}")
    logger.info(f"Schema Registry URL: {args.schema_registry_url}")
    logger.info(f"Output mode: {args.output_mode}")

    try:
        consumer.process_stream(
            output_mode=args.output_mode,
            output_path=args.output_path
        )
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user")
    except Exception as e:
        logger.error(f"Consumer failed: {e}")
        raise


if __name__ == "__main__":
    main()
