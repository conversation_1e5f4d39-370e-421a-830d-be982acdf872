#!/usr/bin/env python
"""
Test script for the debcdc Kafka consumer.
This script demonstrates how to use the DebcdcKafkaConsumer to consume
messages from debcdc.* topics with schema resolution.
"""

import logging
import time
from debcdc_spark_consumer import Debcd<PERSON>KafkaConsumer, SchemaRegistryManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_schema_registry_connection():
    """
    Test connection to Schema Registry and list debcdc subjects.
    """
    logger.info("Testing Schema Registry connection...")
    
    try:
        schema_manager = SchemaRegistryManager("http://localhost:8081")
        subjects = schema_manager.get_all_debcdc_subjects()
        
        logger.info(f"Found {len(subjects)} debcdc subjects:")
        for subject in subjects:
            logger.info(f"  - {subject}")
            
            # Get latest schema info for each subject
            schema_info = schema_manager.get_latest_schema_for_subject(subject)
            if schema_info:
                logger.info(f"    Schema ID: {schema_info['schema_id']}, Version: {schema_info['version']}")
            else:
                logger.warning(f"    Could not fetch schema info for {subject}")
        
        return len(subjects) > 0
    except Exception as e:
        logger.error(f"Error testing Schema Registry connection: {e}")
        return False


def test_consumer_initialization():
    """
    Test consumer initialization and topic discovery.
    """
    logger.info("Testing consumer initialization...")
    
    try:
        consumer = DebcdcKafkaConsumer(
            bootstrap_servers="localhost:9092",
            schema_registry_url="http://localhost:8081",
            app_name="TestDebcdcConsumer",
            checkpoint_location="/tmp/test_debcdc_checkpoint"
        )
        
        topics = consumer.get_debcdc_topics()
        logger.info(f"Discovered topics: {topics}")
        
        return len(topics) > 0
    except Exception as e:
        logger.error(f"Error initializing consumer: {e}")
        return False


def run_consumer_test(duration_seconds: int = 60):
    """
    Run the consumer for a specified duration for testing.
    
    Args:
        duration_seconds: How long to run the test
    """
    logger.info(f"Running consumer test for {duration_seconds} seconds...")
    
    try:
        consumer = DebcdcKafkaConsumer(
            bootstrap_servers="localhost:9092",
            schema_registry_url="http://localhost:8081",
            app_name="TestDebcdcConsumer",
            checkpoint_location="/tmp/test_debcdc_checkpoint"
        )
        
        # Start the consumer in memory mode for testing
        logger.info("Starting consumer in memory mode...")
        
        # Note: In a real test, you would need to run this in a separate thread
        # and stop it after the duration. For now, this will run indefinitely.
        consumer.process_stream(output_mode="console")
        
    except KeyboardInterrupt:
        logger.info("Test stopped by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


def main():
    """
    Main test function.
    """
    logger.info("Starting debcdc consumer tests...")
    
    # Test 1: Schema Registry connection
    if not test_schema_registry_connection():
        logger.error("Schema Registry connection test failed")
        return False
    
    logger.info("✓ Schema Registry connection test passed")
    
    # Test 2: Consumer initialization
    if not test_consumer_initialization():
        logger.error("Consumer initialization test failed")
        return False
    
    logger.info("✓ Consumer initialization test passed")
    
    # Test 3: Run consumer (optional, requires manual interruption)
    logger.info("Starting consumer test (press Ctrl+C to stop)...")
    run_consumer_test()
    
    return True


if __name__ == "__main__":
    main()
