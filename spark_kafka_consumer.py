#!/usr/bin/env python
"""
Spark Streaming application that consumes data from multiple Kafka topics
and integrates with Schema Registry to dynamically fetch the appropriate schema
for each record based on its topic.
"""

import json
import logging
from typing import Dict, Any, Optional

import requests
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroDeserializer
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, from_json, expr
from pyspark.sql.types import StructType, StringType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SchemaRegistryManager:
    """
    Manages interactions with the Schema Registry to dynamically fetch schemas
    for different topics.
    """
    
    def __init__(self, schema_registry_url: str):
        """
        Initialize the Schema Registry manager.
        
        Args:
            schema_registry_url: URL of the Schema Registry
        """
        self.schema_registry_url = schema_registry_url
        self.schema_registry_client = SchemaRegistryClient({'url': schema_registry_url})
        self.schema_cache: Dict[str, Dict[int, str]] = {}  # Cache schemas by topic and version
        
    def get_latest_schema(self, topic: str) -> Optional[str]:
        """
        Get the latest schema for a given topic.
        
        Args:
            topic: Kafka topic name
        
        Returns:
            The latest schema as a string or None if not found
        """
        try:
            subject_name = f"{topic}-value"
            schema_id = self.schema_registry_client.get_latest_version(subject_name).schema_id
            schema = self.schema_registry_client.get_schema(schema_id)
            return schema.schema_str
        except Exception as e:
            logger.error(f"Error fetching schema for topic {topic}: {e}")
            return None
    
    def get_schema_by_id(self, schema_id: int) -> Optional[str]:
        """
        Get a schema by its ID.
        
        Args:
            schema_id: Schema ID
        
        Returns:
            The schema as a string or None if not found
        """
        try:
            schema = self.schema_registry_client.get_schema(schema_id)
            return schema.schema_str
        except Exception as e:
            logger.error(f"Error fetching schema with ID {schema_id}: {e}")
            return None
    
    def get_schema_by_version(self, topic: str, version: int) -> Optional[str]:
        """
        Get a schema for a topic by version.
        
        Args:
            topic: Kafka topic name
            version: Schema version
        
        Returns:
            The schema as a string or None if not found
        """
        # Check cache first
        if topic in self.schema_cache and version in self.schema_cache[topic]:
            return self.schema_cache[topic][version]
        
        try:
            subject_name = f"{topic}-value"
            schema_id = self.schema_registry_client.get_version(subject_name, version).schema_id
            schema = self.schema_registry_client.get_schema(schema_id)
            
            # Cache the schema
            if topic not in self.schema_cache:
                self.schema_cache[topic] = {}
            self.schema_cache[topic][version] = schema.schema_str
            
            return schema.schema_str
        except Exception as e:
            logger.error(f"Error fetching schema for topic {topic} version {version}: {e}")
            return None


class KafkaSchemaConsumer:
    """
    Consumes data from multiple Kafka topics and applies the appropriate schema
    from Schema Registry for each record.
    """
    
    def __init__(
        self,
        bootstrap_servers: str,
        schema_registry_url: str,
        topics: list,
        group_id: str,
        spark_master: str = "local[*]"
    ):
        """
        Initialize the Kafka Schema Consumer.
        
        Args:
            bootstrap_servers: Comma-separated list of Kafka broker addresses
            schema_registry_url: URL of the Schema Registry
            topics: List of Kafka topics to consume
            group_id: Consumer group ID
            spark_master: Spark master URL
        """
        self.bootstrap_servers = bootstrap_servers
        self.schema_registry_url = schema_registry_url
        self.topics = topics
        self.group_id = group_id
        self.spark_master = spark_master
        
        self.schema_registry_manager = SchemaRegistryManager(schema_registry_url)
        self.spark = self._create_spark_session()
        
    def _create_spark_session(self) -> SparkSession:
        """
        Create a SparkSession.
        
        Returns:
            SparkSession instance
        """
        return (
            SparkSession.builder
            .appName("Kafka Schema Registry Integration")
            .master(self.spark_master)
            .config("spark.jars.packages", 
                    "org.apache.spark:spark-sql-kafka-0-10_2.12:3.4.0,"
                    "org.apache.spark:spark-avro_2.12:3.4.0")
            .config("spark.sql.streaming.checkpointLocation", "/tmp/checkpoint")
            .getOrCreate()
        )
    
    def _extract_schema_id_from_payload(self, payload: bytes) -> int:
        """
        Extract the schema ID from the Avro payload.
        
        Args:
            payload: Raw Avro payload with schema ID
        
        Returns:
            Schema ID
        """
        # Confluent Wire Format: First byte is magic byte (0), next 4 bytes are schema ID
        if len(payload) < 5:
            raise ValueError("Payload too short to contain schema ID")
        
        schema_id = int.from_bytes(payload[1:5], byteorder='big')
        return schema_id
    
    def _process_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single record by applying the appropriate schema.
        
        Args:
            record: Raw Kafka record
        
        Returns:
            Processed record with schema applied
        """
        try:
            topic = record.get("topic")
            value = record.get("value")
            
            if not value:
                return {"error": "Empty value", "raw_record": record}
            
            # Extract schema ID from the Avro payload
            schema_id = self._extract_schema_id_from_payload(value)
            
            # Get schema from Schema Registry
            schema_str = self.schema_registry_manager.get_schema_by_id(schema_id)
            if not schema_str:
                return {"error": f"Schema not found for ID {schema_id}", "raw_record": record}
            
            # Deserialize the Avro data
            deserializer = AvroDeserializer(schema_str)
            deserialized_value = deserializer(value[5:])  # Skip magic byte and schema ID
            
            return {
                "topic": topic,
                "schema_id": schema_id,
                "value": deserialized_value
            }
        except Exception as e:
            logger.error(f"Error processing record: {e}")
            return {"error": str(e), "raw_record": record}
    
    def start_streaming(self):
        """
        Start the Spark Streaming job to consume data from Kafka.
        """
        # Create a streaming DataFrame from Kafka
        df = (
            self.spark
            .readStream
            .format("kafka")
            .option("kafka.bootstrap.servers", self.bootstrap_servers)
            .option("subscribe", ",".join(self.topics))
            .option("startingOffsets", "latest")
            .option("failOnDataLoss", "false")
            .load()
        )
        
        # Define a UDF to process each record with the appropriate schema
        def process_record_udf(topic, value):
            try:
                # Extract schema ID from the Avro payload
                if len(value) < 5:
                    return json.dumps({"error": "Payload too short"})
                
                schema_id = int.from_bytes(value[1:5], byteorder='big')
                
                # Get schema from Schema Registry
                schema_str = self.schema_registry_manager.get_schema_by_id(schema_id)
                if not schema_str:
                    return json.dumps({"error": f"Schema not found for ID {schema_id}"})
                
                # For simplicity, return the schema ID and topic in the response
                # In a real application, you would deserialize the Avro data here
                return json.dumps({
                    "topic": topic,
                    "schema_id": schema_id,
                    "schema_found": True
                })
            except Exception as e:
                return json.dumps({"error": str(e)})
        
        # Register the UDF
        self.spark.udf.register("process_record", process_record_udf, StringType())
        
        # Apply the UDF to each record
        processed_df = df.selectExpr(
            "topic", 
            "process_record(topic, value) as processed_value"
        )
        
        # Parse the JSON result
        json_schema = StructType().add("topic", StringType()) \
                                 .add("schema_id", StringType()) \
                                 .add("schema_found", StringType()) \
                                 .add("error", StringType())
        
        parsed_df = processed_df.select(
            col("topic"),
            from_json(col("processed_value"), json_schema).alias("parsed_value")
        ).select(
            col("topic"),
            col("parsed_value.*")
        )
        
        # Start the streaming query
        query = (
            parsed_df
            .writeStream
            .outputMode("append")
            .format("console")
            .start()
        )
        
        # Wait for the query to terminate
        query.awaitTermination()


def main():
    """
    Main entry point for the application.
    """
    # Configuration
    bootstrap_servers = "localhost:9092"
    schema_registry_url = "http://localhost:8081"
    topics = ["topic1", "topic2", "topic3"]  # List of topics to consume
    group_id = "spark-kafka-consumer"
    
    # Create and start the consumer
    consumer = KafkaSchemaConsumer(
        bootstrap_servers=bootstrap_servers,
        schema_registry_url=schema_registry_url,
        topics=topics,
        group_id=group_id
    )
    
    consumer.start_streaming()


if __name__ == "__main__":
    main()
