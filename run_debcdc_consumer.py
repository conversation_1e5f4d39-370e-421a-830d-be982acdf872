#!/usr/bin/env python
"""
Simple runner script for the debcdc Kafka consumer.
This script provides an easy way to start the consumer with common configurations.
"""

import logging
import sys
from debcdc_spark_consumer import DebcdcKafkaConsumer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_console_consumer():
    """
    Run the consumer with console output (default configuration).
    """
    logger.info("Starting debcdc consumer with console output...")
    
    consumer = DebcdcKafkaConsumer(
        bootstrap_servers="localhost:9092",
        schema_registry_url="http://localhost:8081",
        app_name="DebcdcConsoleConsumer",
        checkpoint_location="/tmp/debcdc_console_checkpoint"
    )
    
    try:
        consumer.process_stream(output_mode="console")
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user")
    except Exception as e:
        logger.error(f"Consumer failed: {e}")
        raise


def run_parquet_consumer(output_path: str):
    """
    Run the consumer with parquet output.
    
    Args:
        output_path: Path where parquet files will be saved
    """
    logger.info(f"Starting debcdc consumer with parquet output to {output_path}...")
    
    consumer = DebcdcKafkaConsumer(
        bootstrap_servers="localhost:9092",
        schema_registry_url="http://localhost:8081",
        app_name="DebcdcParquetConsumer",
        checkpoint_location="/tmp/debcdc_parquet_checkpoint"
    )
    
    try:
        consumer.process_stream(output_mode="parquet", output_path=output_path)
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user")
    except Exception as e:
        logger.error(f"Consumer failed: {e}")
        raise


def run_memory_consumer():
    """
    Run the consumer with memory output (for testing).
    """
    logger.info("Starting debcdc consumer with memory output...")
    
    consumer = DebcdcKafkaConsumer(
        bootstrap_servers="localhost:9092",
        schema_registry_url="http://localhost:8081",
        app_name="DebcdcMemoryConsumer",
        checkpoint_location="/tmp/debcdc_memory_checkpoint"
    )
    
    try:
        consumer.process_stream(output_mode="memory")
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user")
    except Exception as e:
        logger.error(f"Consumer failed: {e}")
        raise


def main():
    """
    Main function with command line argument handling.
    """
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python run_debcdc_consumer.py console")
        print("  python run_debcdc_consumer.py parquet <output_path>")
        print("  python run_debcdc_consumer.py memory")
        sys.exit(1)
    
    mode = sys.argv[1].lower()
    
    if mode == "console":
        run_console_consumer()
    elif mode == "parquet":
        if len(sys.argv) < 3:
            print("Error: parquet mode requires output path")
            print("Usage: python run_debcdc_consumer.py parquet <output_path>")
            sys.exit(1)
        output_path = sys.argv[2]
        run_parquet_consumer(output_path)
    elif mode == "memory":
        run_memory_consumer()
    else:
        print(f"Error: Unknown mode '{mode}'")
        print("Supported modes: console, parquet, memory")
        sys.exit(1)


if __name__ == "__main__":
    main()
